import Foundation

extension UserDefaults {
  enum Keys {
    static let serverURL = "serverURL"
    static let preferredOS = "preferredOS"
    static let streamingAppDefault = "streamingAppDefault"
    static let apiKey = "crate2025"
  }

  var serverURL: String? {
    get { string(forKey: Keys.serverURL) }
    set { set(newValue, forKey: Keys.serverURL) }
  }

  var preferredOS: String? {
    get { string(forKey: Keys.preferredOS) }
    set { set(newValue, forKey: Keys.preferredOS) }
  }

  var streamingAppDefault: String? {
    get { string(forKey: Keys.streamingAppDefault) }
    set { set(newValue, forKey: Keys.streamingAppDefault) }
  }

  var apiKey: String {
    get { return Keys.apiKey }
    // swiftlint:disable unused_setter_value
    set { /* Do nothing - we don't want to change the hardcoded value */  }
    // swiftlint:enable unused_setter_value
  }
}
