import Foundation
import SwiftData

// MARK: - Collection Service Protocol

public protocol CollectionServiceProtocol {
  // Remote collections endpoint
  func getCollections(start: Int, size: Int) async throws -> [CollectionDTO]
  func createEmptyCollection(name: String, thumbnail: String?) async throws -> ServerCollectionDTO
  func deleteCollection(serverId: Int) async throws -> Bool
  func addContentToCollection(collectionServerId: Int, contentServerId: Int) async throws -> Bool
  func removeContentFromCollection(collectionServerId: Int, contentServerId: Int) async throws
  -> Bool
}

// MARK: - Server-side DTOs

public struct ServerCollectionDTO: Codable {
  public let id: Int
  public let name: String
  public let thumbnail: String
  public let contents: [ServerContentDTO]
  public let created: String
  public let updated: String

  public init(
    id: Int,
    name: String,
    thumbnail: String,
    contents: [ServerContentDTO],
    created: String,
    updated: String
  ) {
    self.id = id
    self.name = name
    self.thumbnail = thumbnail
    self.contents = contents
    self.created = created
    self.updated = updated
  }

  public func toCollectionModel() -> Collection {
    let isoFormatter = ISO8601DateFormatter()
    return Collection(
      serverId: id,
      name: name,
      content: contents.map { content in
        // Convert ServerContentDTO to Content for local storage
        Content(
          serverId: content.id,
          detail: content.detail,
          title: content.title,
          mediaUrl: content.mediaUrl,
          url: content.url,
          updatedAt: content.updated.flatMap { isoFormatter.date(from: $0) },
          createdAt: content.created.flatMap { isoFormatter.date(from: $0) }
        )
      },
      thumbnail: thumbnail,
      createdAt: isoFormatter.date(from: created) ?? Date.distantPast,
      updatedAt: isoFormatter.date(from: updated) ?? Date.distantPast
    )
  }
}

private struct CreateCollectionRequestDTO: Codable {
  let name: String
  let thumbnail: String
  // Empty collection - no tracks included
}

private struct EmptyParameters: Encodable {}

// MARK: - Collection Service Implementation

public struct CollectionService: CollectionServiceProtocol {
  private let client: HTTPClientProtocol
  private let userState: UserState

  public init(client: HTTPClientProtocol = HTTPClient(), userState: UserState = UserState.shared) {
    self.client = client
    self.userState = userState
  }

  // MARK: - GET Collections Endpoint

  public func getCollections(start: Int = 0, size: Int = 20) async throws -> [CollectionDTO] {
    guard userState.isSignedIn, userState.token != nil else {
      throw CollectionServiceError.notAuthenticated
    }

    struct CollectionParams: Codable {
      let start: Int
      let size: Int
    }

    let params = CollectionParams(start: start, size: size)

    do {
      let serverCollections: [ServerCollectionDTO] = try await client.get(
        path: "/api/v1/collection",
        parameters: params
      )

      let isoFormatter = ISO8601DateFormatter()
      return serverCollections.map { collection in
        CollectionDTO(
          serverId: collection.id,
          name: collection.name,
          thumbnail: collection.thumbnail,
          contents: collection.contents.map { content in
            ContentDTO(
              serverId: content.id,
              detail: content.detail,
              title: content.title,
              mediaUrl: content.mediaUrl,
              url: content.url,
              updatedAt: content.updated.flatMap { isoFormatter.date(from: $0) },
              createdAt: content.created.flatMap { isoFormatter.date(from: $0) }
            )
          },
          createdAt: isoFormatter.date(from: collection.created) ?? Date.distantPast,
          updatedAt: isoFormatter.date(from: collection.updated) ?? Date.distantPast
        )
      }
    } catch {
      print("🔴 Get collections error: \(error.localizedDescription)")
      throw CollectionServiceError.getCollectionsFailed
    }
  }

  // MARK: - POST Collection Endpoint

  public func createEmptyCollection(name: String, thumbnail: String? = nil) async throws
  -> ServerCollectionDTO {
    guard userState.isSignedIn, userState.token != nil else {
      throw CollectionServiceError.notAuthenticated
    }

    let requestDTO = CreateCollectionRequestDTO(
      name: name,
      thumbnail: thumbnail ?? ""
    )

    do {
      print("📤 Sending empty collection creation request with name: \(name)")

      let serverCollection: ServerCollectionDTO = try await client.post(
        path: "/api/v1/collection",
        body: requestDTO
      )

      return serverCollection
    } catch {
      print("🔴 Collection creation error: \(error.localizedDescription)")
      throw CollectionServiceError.createCollectionFailed
    }
  }

  // MARK: - Add Content to Collection Endpoint

  public func addContentToCollection(collectionServerId: Int, contentServerId: Int) async throws
  -> Bool {
    guard userState.isSignedIn, userState.token != nil else {
      throw CollectionServiceError.notAuthenticated
    }

    do {
      let _: ServerCollectionDTO = try await client.post(
        path: "/api/v1/collection/\(collectionServerId)/add",
        body: [contentServerId]
      )
      return true
    } catch {
      print("🔴 Adding content to collection error: \(error.localizedDescription)")
      throw CollectionServiceError.addContentToCollectionFailed
    }
  }

  // MARK: - Remove Content from Collection Endpoint

  public func removeContentFromCollection(collectionServerId: Int, contentServerId: Int)
  async throws
  -> Bool {
    guard userState.isSignedIn, userState.token != nil else {
      throw CollectionServiceError.notAuthenticated
    }

    do {
      let _: ServerCollectionDTO = try await client.post(
        path: "/api/v1/collection/\(collectionServerId)/remove",
        body: [contentServerId]
      )
      return true
    } catch {
      print("🔴 Removing content from collection error: \(error.localizedDescription)")
      throw CollectionServiceError.removeContentFromCollectionFailed
    }
  }

  // MARK: - DELETE Collection Endpoint

  public func deleteCollection(serverId: Int) async throws -> Bool {
    guard userState.isSignedIn, userState.token != nil else {
      throw CollectionServiceError.notAuthenticated
    }

    do {
      _ = try await client.delete(path: "/api/v1/collection/\(serverId)", body: EmptyParameters())
      return true
    } catch {
      print("🔴 Collection deletion error: \(error.localizedDescription)")
      throw CollectionServiceError.deleteCollectionFailed
    }
  }

}

// MARK: - Collection Service Errors

public enum CollectionServiceError: Error {
  case notAuthenticated
  case getCollectionsFailed
  case createCollectionFailed
  case deleteCollectionFailed
  case addContentToCollectionFailed
  case removeContentFromCollectionFailed
}
