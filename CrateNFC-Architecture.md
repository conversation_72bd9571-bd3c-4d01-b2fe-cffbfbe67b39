# CrateNFC iOS App Architecture

## Overview

CrateNFC is an iOS app built with SwiftUI that allows users to write NFC tags with content URLs and manage collections of content. The app follows a clean, layered architecture with strict separation of concerns, emphasizing thread safety, testability, and maintainability.

## Architecture Principles

### 🎯 **Core Design Goals**

- **Thread Safety**: All SwiftData operations are isolated in actors
- **Separation of Concerns**: Clear boundaries between data, business logic, and UI
- **Testability**: Dependency injection and protocol-based design
- **Maintainability**: Single responsibility principle throughout
- **Performance**: Efficient data flow and minimal UI blocking

### 📐 **Architectural Patterns**

- **Layered Architecture**: Clear separation between presentation, business, and data layers
- **Repository Pattern**: CrateActor as centralized data access layer
- **Service Layer**: HTTP operations and external integrations separated from data access
- **Presentation Controller Pattern**: ViewModels orchestrate between services rather than traditional MVVM
- **Actor Model**: Thread-safe data operations with Swift Concurrency
- **Publisher-Subscriber**: Reactive state management with Combine

## Layer Architecture

This app follows a **4-layer architecture** with clear separation of concerns:

```text
┌─────────────────────────────────────────────────────────────┐
│                   Presentation Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Views     │  │Presentation │  │   UI Components     │  │
│  │ (SwiftUI)   │  │Controllers  │  │   (Reusable)        │  │
│  │             │  │(ViewModels) │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Business Logic Layer                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │  Services   │  │    State    │  │   Authentication    │  │
│  │ (HTTP/API)  │  │ Management  │  │     (MSAL)          │  │
│  │             │  │(UserState)  │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Data Access Layer                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ CrateActor  │  │    DTOs     │  │   Data Transfer     │  │
│  │(@ModelActor)│  │ (Sendable)  │  │   & Conversion      │  │
│  │             │  │             │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Persistence Layer                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ SwiftData   │  │   Models    │  │   Relationships     │  │
│  │ (Storage)   │  │ (@Model)    │  │   & Constraints     │  │
│  │             │  │             │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Data Flow Architecture

### 📊 **Primary Data Flow**

```text
API Request → Service → DTO → CrateActor → SwiftData Models → Storage
                                    ↓
UI ← Presentation Controller ← DTO ← CrateActor ← SwiftData Models ← Storage
```

### 🔄 **Key Flow Principles**

1. **API → Service**: HTTP operations return DTOs
2. **Service → CrateActor**: DTOs passed for persistence
3. **CrateActor → SwiftData**: DTOs converted to @Model objects
4. **SwiftData → CrateActor**: Models converted back to DTOs
5. **CrateActor → Presentation Controller**: Only DTOs leave the actor
6. **Presentation Controller → UI**: DTOs used directly or converted for display

## Architecture Classification

### 🏗️ **Why This Isn't Traditional MVVM**

While the files are named `*ViewModel.swift`, this architecture **does not follow traditional MVVM patterns**:

**Traditional MVVM**:

- **Model**: Contains business logic and data
- **View**: Passive UI that binds to ViewModel
- **ViewModel**: Contains presentation logic, directly manipulates models

**This Architecture**:

- **Models**: Pure data containers (`@Model` classes) isolated in CrateActor
- **Views**: SwiftUI views that observe presentation controllers
- **Presentation Controllers**: Coordinate between services, contain no business logic
- **Services**: Contain the actual business logic
- **CrateActor**: Repository pattern for data access

### 🎯 **Actual Pattern: Layered Architecture with Coordination**

This is better described as:

- **Layered Architecture** with clear separation between presentation, business, and data layers
- **Repository Pattern** via CrateActor for centralized data access
- **Service Layer Pattern** for business logic separation
- **Presentation Controller Pattern** for UI coordination (not ViewModels)

The "ViewModels" are actually **coordinators** that orchestrate between specialized services rather than containing business logic themselves.

## Core Components

### 🎭 **CrateActor** - The Data Access Layer

**Location**: `CrateServices/Actor/CrateActor.swift`

```swift
@ModelActor
public actor CrateActor {
  // Thread-safe SwiftData operations
}
```

**Responsibilities**:

- **ONLY** place where `ModelContext` is used
- All CRUD operations for SwiftData models
- Thread-safe data access via actor isolation
- DTO ↔ Model conversion
- Complex relationship management
- Data consistency enforcement

**Key Methods**:

- `fetchCollections() -> [CollectionDTO]`
- `addContent(_:toCollectionWithServerId:)`
- `syncCollectionsFromServer(_:)`
- `clearAllUserData()`

### 🌐 **Services** - External Integration Layer

**Location**: `CrateServices/Services/`

**Service Types**:

- **ContentService**: Content API operations
- **CollectionService**: Collection API operations  
- **UserService**: Authentication with Microsoft Entra ID
- **RegistrationService**: User registration flow

**Responsibilities**:

- **ONLY** HTTP operations and external API calls
- Return DTOs from API responses
- Handle network errors and retries
- Authentication token management
- **NO** SwiftData operations

**Example**:

```swift
public protocol ContentServiceProtocol {
  func getTrending() async throws -> [ContentDTO]
  func unfurl(url: String) async throws -> ContentDTO
}
```

### 📱 **Presentation Controllers** - UI Coordination Layer

**Location**: `CrateNFC/App/CrateNFC/Views/*/`
**Naming**: `*ViewModel.swift` (historical naming, but functionally presentation controllers)

**Responsibilities**:

- **Orchestrate** between Services and CrateActor (not traditional ViewModel logic)
- **Coordinate** multiple services and data sources
- **Manage** UI state with `@Published` properties
- **Handle** user interactions and navigation
- **Transform** DTOs for UI consumption when needed
- **NO** direct SwiftData operations
- **NO** business logic (delegated to Services)

**Key Difference from Traditional MVVM**:
These are **coordinators/orchestrators** rather than ViewModels. They don't contain business logic or directly manipulate models - they coordinate between specialized services.

**Pattern**:

```swift
@MainActor
public final class ExampleViewModel: ObservableObject {
  // Dependencies injected
  private let service: ServiceProtocol
  private let crateActor: CrateActor

  func performAction() {
    Task {
      // 1. Coordinate API call
      let dtos = try await service.getData()

      // 2. Coordinate data persistence
      try await crateActor.saveData(dtos)

      // 3. Update UI state
      self.updateState()
    }
  }
}
```

## Data Models & DTOs

### 🗃️ **SwiftData Models** - Persistent Storage

**Location**: `CrateServices/Models/`

**Core Models**:

- **User**: User account information
- **Content**: Individual content items (URLs, metadata)
- **Collection**: User-created content collections
- **TrendingContent**: Cached trending content
- **RecentCollection**: Recently accessed collections

**Characteristics**:

- Marked with `@Model` for SwiftData persistence
- Contain relationships (e.g., Collection ↔ Content many-to-many)
- Include `toDTO()` conversion methods
- **ONLY** used within CrateActor

**Example**:

```swift
@Model
public final class Collection {
  public var serverId: Int?
  public var name: String?
  public var content: [Content] // Many-to-many relationship

  public func toDTO() -> CollectionDTO {
    CollectionDTO(
      serverId: serverId,
      name: name,
      contents: content.map { $0.toDTO() }
    )
  }
}
```

### 📦 **DTOs (Data Transfer Objects)** - Thread-Safe Data

**Location**: `CrateServices/Models/` (alongside models)

**Design Pattern**: `struct + Sendable + Codable + let`

**Characteristics**:

- **struct**: Value semantics, no reference sharing
- **Sendable**: Automatic compiler synthesis (no manual conformance needed)
- **Codable**: Seamless JSON encoding/decoding for API communication
- **Identifiable + Hashable**: Efficient SwiftUI diffing and ForEach performance
- **Immutable**: All properties are `let` - read-only snapshots
- **Cross-actor safe**: Value copying prevents aliasing bugs

**Benefits over class DTOs**:

- ✅ **Automatic Sendable**: Compiler synthesizes thread safety
- ✅ **Value semantics**: No shared mutable state across actors
- ✅ **SwiftUI performance**: Efficient list diffing with Hashable
- ✅ **Safer decoding**: Value types align with "data snapshot" semantics
- ✅ **Immutability contract**: Prevents accidental mutations

**Example**:

```swift
public struct CollectionDTO: Sendable, Codable, Identifiable, Hashable {
  public let serverId: Int?
  public let name: String?
  public let contents: [ContentDTO]?

  // Computed property for Identifiable - stable across decoding
  public var id: String {
    if let serverId = serverId {
      return "collection-\(serverId)"
    }
    return name ?? "unknown-collection"
  }

  public func toModel() -> Collection {
    Collection(
      serverId: serverId,
      name: name,
      content: contents?.map { $0.toModel() } ?? []
    )
  }

  // Hashable & Equatable auto-synthesized by compiler
}
```

## State Management

### 👤 **UserState** - Global User Session

**Location**: `CrateServices/Utilities/UserState.swift`

**Responsibilities**:

- Current user information (`@Published`)
- Authentication token storage (`@AppStorage`)
- Login/logout state management
- Profile updates

**Integration**:

```swift
public class UserState: ObservableObject {
  @Published public var currentUser: User?
  @AppStorage("userToken") public var token: String?

  public var isSignedIn: Bool {
    return currentUser != nil && token != nil
  }
}
```

### 📡 **AuthPublisher** - Authentication Events

**Location**: `CrateServices/Utilities/AuthPublisher.swift`

**Purpose**: Reactive authentication state changes across the app

**Events**:

- `.signedIn(User)`: User successfully authenticated
- `.signedOut`: User logged out
- `.tokenRefreshed(String)`: Token updated
- `.profileUpdated(User)`: Profile information changed

**Usage Pattern**:

```swift
AuthPublisher.shared.publisher
  .receive(on: DispatchQueue.main)
  .sink { event in
    switch event {
    case .signedIn:
      // Refresh data, update UI
    case .signedOut:
      // Clear data, show login
    }
  }
  .store(in: &cancellables)
```

## Authentication Architecture

### 🔐 **Microsoft Entra ID Integration**

**Technology**: MSAL (Microsoft Authentication Library)
**Configuration**:

- Client ID: `6d030d81-5d4a-42d1-a4c8-76c03b13fcc4`
- Tenant: `cratenfc.ciamlogin.com`
- Scopes: `["api://cratenfc/access"]`
- Challenge Types: Email + Password, One-Time Passcode

**Flow**:

1. User enters credentials in UI
2. MSAL handles authentication with Microsoft Entra
3. Access token retrieved and stored
4. Token used for API authentication
5. User profile fetched and stored in UserState
6. AuthPublisher notifies app of sign-in

### 🔄 **Token Management**

- Automatic token refresh via MSAL
- HTTPClient automatically includes bearer token
- Token stored securely in UserDefaults
- Logout clears both local and MSAL cached tokens

## Dependency Injection

### 🏭 **Factory Pattern**

**Library**: [Factory](https://github.com/hmlongco/Factory)
**Location**: `CrateNFC/App/CrateNFC/CrateNFCApp.swift`

**Scopes**:

- **`.singleton`**: Single instance app-wide (UserState, CrateActor)
- **`.cached`**: Reused within resolution chain (Services)
- **`.shared`**: Scoped to container lifetime

**Example Configuration**:

```swift
extension Container {
  var crateActor: Factory<CrateActor> {
    self {
      let modelContainer = self.modelContext().container
      return CrateActor(modelContainer: modelContainer)
    }.singleton // Thread-safe, consistent data access
  }

  var contentService: Factory<ContentServiceProtocol> {
    self {
      ContentService(client: self.httpClient.resolve())
    }.cached // Fresh instance per resolution chain
  }
}
```

## Thread Safety & Concurrency

### ⚡ **Swift Concurrency Integration**

- **CrateActor**: `@ModelActor` ensures thread-safe SwiftData access
- **Presentation Controllers**: `@MainActor` for UI updates and coordination
- **Services**: Async/await for network operations
- **DTOs**: `Sendable` conformance for cross-actor communication

### 🔒 **Concurrency Patterns**

```swift
// Presentation Controller coordinates async operations
@MainActor
func loadData() {
  Task {
    // 1. Network call (background)
    let dtos = try await service.fetchData()

    // 2. Data persistence (actor-isolated)
    try await crateActor.saveData(dtos)

    // 3. UI update (main actor)
    self.updateUI()
  }
}
```

## Testing & Performance

### 🧪 **Testing Strategy**

- **Unit Tests**: Service protocols with mock implementations
- **Integration Tests**: CrateActor with in-memory SwiftData
- **Presentation Layer Tests**: Presentation Controller testing with dependency injection
- **Mock Objects**: Comprehensive mocking for external dependencies
- **Coordination Tests**: Testing the orchestration between services and data layer

### ⚡ **Performance Optimizations**

- **Singleton CrateActor**: Reduces memory overhead
- **DTO Conversion**: Minimal object creation
- **Background Processing**: Network operations off main thread
- **SwiftData Efficiency**: Optimized fetch descriptors

### 🔐 **Security Measures**

- **Token Storage**: Secure storage via MSAL and UserDefaults
- **HTTPS Only**: All API communication encrypted
- **Token Refresh**: Automatic token renewal
- **Input Validation**: URL and data validation throughout

---

## Summary

The CrateNFC architecture provides a robust, scalable foundation with:

✅ **Thread-Safe Data Access** via CrateActor
✅ **Clean Separation of Concerns** across all layers
✅ **Reactive State Management** with Combine
✅ **Comprehensive Testing Support** via dependency injection
✅ **Modern Swift Concurrency** throughout
✅ **Secure Authentication** with Microsoft Entra ID
✅ **Performance Optimized** data flow

This architecture ensures maintainable, testable, and performant code that can scale with the application's growth.
