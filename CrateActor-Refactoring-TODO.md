# CrateActor Refactoring TODO List

## Overview

This document tracks the refactoring needed to ensure all SwiftData operations go through Crate<PERSON>ctor instead of direct ModelContext usage. CrateActor provides thread-safe, centralized data access and should be the single point of entry for all SwiftData operations.

## High Priority Refactoring Tasks

### 1. CrateNFCApp.swift - Clear User Data Function ✅

- [x] **File**: `CrateNFC/App/CrateNFC/CrateNFCApp.swift`
- [x] **Issue**: Direct ModelContext usage in `clearUserData()` function (lines 47-87)
- [x] **Action**: Add `clearAllUserData()` method to CrateActor
- [x] **Details**: Replace direct fetch/delete operations with CrateActor method
- [x] **Impact**: High - This is a critical data operation that should be centralized

### 2. TrendingViewModel - Remove Direct ModelContext Usage ✅

- [x] **File**: `CrateNFC/App/CrateNFC/Views/Trending/TrendingViewModel.swift`
- [x] **Issue**: Uses ContentService with direct ModelContext (lines 30-31, 70-71, 85-88)
- [x] **Action**: Add trending content methods to <PERSON>rateActor and update TrendingViewModel
- [x] **Methods needed in CrateActor**:
  - [x] `saveTrendingContent(_ content: [ContentDTO]) async throws`
  - [x] `getAllTrendingContent() async throws -> [ContentDTO]`
  - [x] `deleteAllTrendingContent() async throws`
- [x] **Impact**: Medium - Affects trending content display and caching

### 3. ContentService - Migrate SwiftData Operations ✅

- [x] **File**: `CrateServices/Services/ContentService.swift`
- [x] **Issue**: Direct ModelContext operations in SwiftData methods (lines 19-28, 71-174)
- [x] **Action**: Move SwiftData operations to CrateActor, keep only HTTP operations in ContentService
- [x] **SwiftData methods migrated to CrateActor**:
  - [x] `getAllTrendingContent()` (SwiftData fetch operation)
  - [x] `deleteAllTrendingContent()` (SwiftData delete operation)
  - [x] `saveTrendingContent()` (SwiftData save operation)
  - [x] `getContentByServerId()` (SwiftData fetch operation)
- [x] **Kept in ContentService** (API operations):
  - [x] `getTrending()` (HTTP GET)
  - [x] `getLatest()` (HTTP GET)
  - [x] `unfurl()` (HTTP POST)
- [x] **Impact**: High - Core service used throughout the app

### 4. CollectionService - Remove SwiftData Operations ✅

- [x] **File**: `CrateServices/Services/CollectionService.swift`
- [x] **Issue**: Direct ModelContext usage in `saveCollections()` method (lines 225-229)
- [x] **Action**: Remove SwiftData operations from CollectionService, use CrateActor instead
- [x] **Details**: CollectionService should only handle HTTP operations, not data persistence
- [x] **Impact**: Medium - Used for collection synchronization

### 5. CollectionViewModel - Complete CrateActor Migration ✅

- [x] **File**: `CrateNFC/App/CrateNFC/Views/Profile/Collection/CollectionViewModel.swift`
- [x] **Issue**: Mixed usage - some operations use CrateActor, others use direct ModelContext
- [x] **Direct ModelContext usage locations**:
  - [x] Line 226: `modelContext.insert(newContent)`
  - [x] Line 261: `modelContext.insert(newContent)`
  - [x] Line 269: `modelContext.insert(newCollection)`
  - [x] Lines 150-272: `syncCollectionsFromServer()` method
- [x] **Action**: Refactor to use only CrateActor methods
- [x] **Impact**: High - Core collection management functionality

### 6. AddUrlToCollectionViewModel - Remove Legacy ModelContext Usage ✅

- [x] **File**: `CrateNFC/App/CrateNFC/Views/Profile/Collection/AddUrlToCollectionViewModel.swift`
- [x] **Issue**: Legacy `addContentToCollection()` method uses direct ModelContext (lines 58-64)
- [x] **Action**: Remove unused legacy method, ensure only CrateActor usage
- [x] **Details**: Method appears unused since CrateActor integration
- [x] **Impact**: Low - Legacy code cleanup

## Medium Priority Tasks

### 7. Environment ModelContext Cleanup ✅

- [x] **Files**: Multiple view files
- [x] **Issue**: Views still inject `@Environment(\.modelContext)` but may not need it
- [x] **Files reviewed and cleaned**:
  - [x] `RecentView.swift` (line 21) - Removed unused modelContext
  - [x] `AddUrlToCollectionSheet.swift` (line 10) - Removed unused modelContext
  - [x] `WriteView.swift` (line 10) - Removed unused modelContext
  - [x] `CollectionView.swift` (line 22) - Removed unused modelContext
  - [x] `AddContentToCollectionSheet.swift` (line 15) - Removed unused modelContext
- [x] **Action**: Remove unused ModelContext environment injections
- [x] **Impact**: Low - Code cleanup and performance

### 8. Add Missing CrateActor Methods ✅

- [x] **File**: `CrateServices/Actor/CrateActor.swift`
- [x] **Action**: Add methods needed by refactored components
- [x] **SwiftData methods added**:
  - [x] `clearAllUserData() async throws`
  - [x] `saveTrendingContent(_ content: [ContentDTO]) async throws`
  - [x] `getAllTrendingContent() async throws -> [ContentDTO]`
  - [x] `deleteAllTrendingContent() async throws`
  - [x] `syncCollectionsFromServer(_ collections: [ServerCollectionDTO]) async throws`
- [x] **Impact**: High - Required for other refactoring tasks

## Low Priority Tasks

### 9. Service Protocol Updates ✅

- [x] **Files**: Service protocol files
- [x] **Issue**: Service protocols still define SwiftData methods
- [x] **Action**: Remove SwiftData methods from service protocols
- [x] **Files updated**:
  - [x] `ContentServiceProtocol` - Removed SwiftData methods (completed in Task 3)
  - [x] `CollectionServiceProtocol` - Reviewed and confirmed clean (completed in Task 4)
- [x] **Impact**: Low - API cleanup

### 10. Factory Configuration Review ✅

- [x] **File**: `CrateNFC/App/CrateNFC/CrateNFCApp.swift`
- [x] **Issue**: Review CrateActor factory scope configuration
- [x] **Previous**: `.cached` scope
- [x] **Updated**: `.singleton` scope - More appropriate for @ModelActor
- [x] **Rationale**: Thread-safe access, reduced memory overhead, consistency across app
- [x] **Impact**: Low - Performance optimization

## Testing Requirements

### After Refactoring

- [ ] **Unit Tests**: Create tests for new CrateActor methods
- [ ] **Integration Tests**: Test data flow through CrateActor
- [ ] **UI Tests**: Verify all data operations still work correctly
- [ ] **Performance Tests**: Ensure no performance regression
- [ ] **Concurrency Tests**: Verify thread safety of CrateActor operations

## Benefits of Completion

1. **Thread Safety**: All SwiftData operations will be properly isolated in the actor
2. **Consistency**: Single source of truth for data operations
3. **Maintainability**: Centralized data logic easier to maintain and debug
4. **Testing**: Easier to mock and test data operations
5. **Performance**: Better concurrency handling with actor isolation
6. **Architecture**: Clean separation between HTTP services and data persistence

## Architecture Principles

- **CrateActor**: ONLY place where `modelContext` is used (SwiftData operations)
- **Services** (ContentService, CollectionService): ONLY handle HTTP operations and return DTOs
- **ViewModels**: ONLY interact with CrateActor for data operations and Services for API calls
- **Data Flow**: API → Service → DTO → CrateActor → SwiftData
- **Return Flow**: SwiftData → CrateActor → DTO → ViewModel → UI
- All SwiftData models should be converted to DTOs before leaving CrateActor
- Consider adding error handling and logging to new CrateActor methods

## Naming Conventions

- **API operations**: `getTrending()`, `getLatest()`, `unfurl()` (in Services)
- **SwiftData operations**: `saveTrendingContent()`, `getAllTrendingContent()`, `deleteAllTrendingContent()` (in CrateActor)
