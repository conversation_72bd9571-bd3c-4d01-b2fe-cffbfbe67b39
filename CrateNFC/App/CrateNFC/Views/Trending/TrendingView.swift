import CrateServices
import SwiftData
import SwiftUI

struct TrendingView: View {
  @Binding private var selectedTab: NavView.Tab
  private var deepLinkHandler: DeepLinkHandler
  @StateObject private var viewModel: TrendingViewModel
  @State private var showingAddToCollectionSheet = false
  @State private var selectedContent: Content?

  public init(selectedTab: Binding<NavView.Tab>, deepLinkHandler: DeepLinkHandler) {
    self._selectedTab = selectedTab
    self._viewModel = StateObject(wrappedValue: TrendingViewModel())
    self.deepLinkHandler = deepLinkHandler
  }

  var body: some View {
    NavigationView {
      ScrollView {
        LazyVStack(spacing: 16) {
          switch viewModel.state {
          case .content(let content):
            ForEach(content) { item in
              HStack {
                Button(
                  action: { contentLinkClicked(item) },
                  label: {
                    ContentRow(content: item)
                      .padding(.vertical, 8)
                  })

                Spacer()

                // Add + button for adding to collection
                But<PERSON>(
                  action: {
                    selectedContent = item
                    showingAddToCollectionSheet = true
                  },
                  label: {
                    Image(systemName: "plus.circle.fill")
                      .foregroundColor(.blue)
                      .font(.title3)
                      .contentShape(Rectangle())
                      .frame(width: 44, height: 44)
                  }
                )
                .buttonStyle(BorderlessButtonStyle())
                .padding(.trailing, 8)
              }
              .padding(.horizontal, 16)
              .listRowInsets(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 0))
              .listRowBackground(Color.clear)

              Divider()
            }
          case .loading:
            ProgressView()
          case .empty:
            Text("No trending content available")
          case .error(let message):
            Text(message)
              .foregroundColor(.red)
          }
        }
        .navigationTitle("Trending Content")
        .onAppear {
          viewModel.loadCachedContent()
          if case .empty = viewModel.state {
            viewModel.fetchTrendingContent()
          }
        }
      }
      .refreshable {
        viewModel.fetchTrendingContent()
      }
      .sheet(
        isPresented: $showingAddToCollectionSheet,
        onDismiss: {
          // Don't reset the content selection immediately on dismiss to avoid issues
          DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            selectedContent = nil
          }
        },
        content: {
          // Capture the selected content in a local constant to prevent issues
          if let content = selectedContent {
            AddContentToCollectionSheet(
              content: content.toDTO(),
              onDismiss: { showingAddToCollectionSheet = false }
            )
          } else {
            Text("No content selected")
              .foregroundColor(.secondary)
              .padding()
          }
        })
    }
  }

  private func contentLinkClicked(_ content: Content) {
    self.deepLinkHandler.deepLinkURL = URL(string: content.url ?? "")
    self.selectedTab = .write  // Switch to Write tab
  }
}
@ViewBuilder
private func getImage(_ imageUrl: URL) -> some View {
  AsyncImage(url: imageUrl) { image in
    image.resizable()
      .aspectRatio(contentMode: .fit)
      .frame(width: 80, height: 80)
      .cornerRadius(8)
  } placeholder: {
    ProgressView()
      .frame(width: 80, height: 80)
  }
}
struct ContentRow: View {
  let content: Content

  var body: some View {
    HStack(alignment: .center, spacing: 12) {
      if let imageUrl = content.mediaUrl.flatMap({ URL(string: $0) }) {
        AsyncImage(url: imageUrl) { image in
          image.resizable()
            .aspectRatio(contentMode: .fill)
            .frame(width: 60, height: 60)
            .cornerRadius(8)
        } placeholder: {
          ProgressView()
            .frame(width: 60, height: 60)
        }
      } else {
        // Default placeholder for content without images
        Image(systemName: "music.note")
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: 60, height: 60)
          .padding(12)
          .background(Color.gray.opacity(0.2))
          .cornerRadius(8)
      }

      VStack(alignment: .leading, spacing: 4) {
        Text(content.title ?? "Untitled Content")
          .font(.system(size: 16, weight: .semibold))
          .foregroundColor(.blue)
          .lineLimit(1)

        Text(content.detail ?? content.url ?? "-")
          .font(.system(size: 14))
          .foregroundColor(.gray)
          .lineLimit(1)
          .truncationMode(.tail)
      }

      Spacer()
    }
    .frame(maxWidth: .infinity)
    .contentShape(Rectangle())
  }
}

#Preview {
  TrendingView(
    selectedTab: .constant(.trending),
    deepLinkHandler: DeepLinkHandler()
  )
}
