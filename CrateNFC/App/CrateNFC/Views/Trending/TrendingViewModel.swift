import CrateServices
import Factory
import SwiftData
import SwiftUI

public enum TrendingViewState {
  case loading
  case error(String)
  case empty
  case content([Content])
}

@MainActor
public final class TrendingViewModel: ObservableObject {
  @Published public private(set) var state: TrendingViewState = .empty

  private let apiService: ApiServiceProtocol
  private let contentService: ContentServiceProtocol
  private let crateActor: CrateActor

  public init() {
    self.apiService = Container.shared.apiService.resolve()
    self.contentService = Container.shared.contentService.resolve()
    self.crateActor = Container.shared.crateActor.resolve()
  }

  public func loadCachedContent() {
    if case .content = state {
      return
    }

    Task { @MainActor in
      do {
        let contentDTOs = try await crateActor.getAllTrendingContent()
        let contentModels = contentDTOs.map { $0.toModel() }
        updateState(contentModels)
      } catch {
        print("Error loading cached trending content: \(error.localizedDescription)")
        state = .empty
      }
    }
  }

  private func updateState(_ records: [Content]) {
    if records.isEmpty {
      state = .empty
    } else {
      state = .content(records)
    }
  }

  public func fetchTrendingContent() {
    Task { @MainActor in
      do {
        self.state = .loading

        // Fetch trending content from API
        let contentDTOs = try await contentService.getTrending()

        // Clear old trending content and save new content via CrateActor
        try await crateActor.deleteAllTrendingContent()
        try await crateActor.saveTrendingContent(contentDTOs)

        // Convert DTOs to models for UI display
        let models = contentDTOs.map { $0.toModel() }
        self.state = .content(models)

        print("Successfully fetched and saved \(contentDTOs.count) trending content items")
      } catch {
        print("Error fetching trending content: \(error.localizedDescription)")

        // Try to load cached content from CrateActor
        do {
          let cachedContentDTOs = try await crateActor.getAllTrendingContent()
          if !cachedContentDTOs.isEmpty {
            let cachedModels = cachedContentDTOs.map { $0.toModel() }
            self.state = .content(cachedModels)
            return
          }
        } catch {
          print("Error loading cached trending content: \(error.localizedDescription)")
        }

        self.state = .error(error.localizedDescription)
      }
    }
  }

}
