import Combine
import CrateServices
import Factory
import SwiftData
import Swift<PERSON>

@MainActor
public final class RecentViewModel: ObservableObject {
  @Published public private(set) var content: [ContentDTO] = []
  @Published public private(set) var isLoading = false
  @Published public private(set) var isDeletingContent = false

  // Services
  private let contentService: ContentServiceProtocol
  private let userState: UserState
  private var crateActor: CrateActor

  // Cancellables for tracking subscriptions
  private var cancellables = Set<AnyCancellable>()

  // Add state tracking to prevent infinite loops
  private var isRefreshingFromServer = false
  private var lastServerFetchTime: Date?
  private let minimumRefreshInterval: TimeInterval = 5.0  // 5 seconds between refreshes

  public init() {
    self.crateActor = Container.shared.crateActor.resolve()
    contentService = Container.shared.contentService.resolve()
    userState = Container.shared.userState.resolve()

    // Subscribe to Auth events with receive(on:) to ensure main thread
    AuthPublisher.shared.publisher
      .receive(on: DispatchQueue.main)
      .sink { [weak self] event in
        guard let self = self else { return }

        switch event {
        case .signedIn:
          Task {
            await self.ensureRecentWritesCollectionExists()
            // Use the same pattern - SwiftData first, then background sync
            self.loadFromSwiftDataFirst()
            self.backgroundSyncWithServer()
          }
        case .signedOut:
          // Clear all local content when signing out and ensure UI is updated
          self.content = []

          // Then proceed with the database cleanup
          Task {
            await self.clearAllLocalContent()

            // Prevent automatic reload of content for guest users after sign-out
            await MainActor.run {
              self.content = []  // Ensure content remains empty after cleanup
            }
          }
        case .tokenRefreshed, .profileUpdated, .userCreated:
          break
        }
      }
      .store(in: &cancellables)

    // Initial load - always start with SwiftData
    Task {
      await ensureRecentWritesCollectionExists()
      // Use the same pattern as loadContent() - SwiftData first, then background sync
      loadFromSwiftDataFirst()
      if userState.isSignedIn {
        backgroundSyncWithServer()
      }
    }
  }

  // Ensure the Recent collection exists locally
  private func ensureRecentWritesCollectionExists() async {
    do {
      let recentCollection = try await crateActor.fetchRecentCollection()
      if recentCollection == nil {
        // Create locally if it doesn't exist
        try await crateActor.addRecentCollection(
          name: "Recent",
          thumbnail: "crate_logo",
          content: []
        )
      }
    } catch {
      print("Error ensuring 'Recent' collection exists: \(error.localizedDescription)")
    }
  }

  private func updateRecentCollection(_ content: [ContentDTO]) async throws {
    try await crateActor.updateRecentCollection(content: content)
  }

  private func updateViewModelState(_ content: [ContentDTO]) {
    // Content is already sorted in CrateActor, no need to sort again
    self.content = content
    self.lastServerFetchTime = Date()
    self.isRefreshingFromServer = false
    self.isLoading = false
  }

  // Fetch content from server - only called on sign in
  private func fetchContentFromServer() {
    // Prevent concurrent refreshes
    if isRefreshingFromServer {
      return
    }

    isRefreshingFromServer = true
    isLoading = true

    Task {
      do {
        // Fetch fresh content from server endpoint
        let fetchedContentDTOs = try await contentService.getLatest(start: 0, size: 50)

        // Remove duplicates
        let uniqueContent = removeDuplicateContent(fetchedContentDTOs)

        // Update the Recent collection
        try await updateRecentCollection(uniqueContent)

        // Update the view model's state
        await MainActor.run {
          updateViewModelState(uniqueContent)
        }
      } catch {
        print("Error fetching content from server: \(error.localizedDescription)")
        await MainActor.run {
          self.isRefreshingFromServer = false
          self.isLoading = false
        }
      }
    }
  }

  // Public load function - always load from SwiftData first, then sync in background
  public func loadContent() {
    // Skip if we're already refreshing from server
    if isRefreshingFromServer {
      return
    }

    // Always load from local SwiftData first (fast, no loading spinner)
    loadFromSwiftDataFirst()

    // Then optionally sync with server in background for authenticated users
    if userState.isSignedIn {
      backgroundSyncWithServer()
    }
  }

  // Load from SwiftData first - this should be fast and not show loading spinner
  private func loadFromSwiftDataFirst() {
    Task {
      do {
        let recentCollection = try await crateActor.fetchRecentCollection()
        // Content is already sorted in CrateActor, no need to sort again
        let contentDTOs = recentCollection?.contents ?? []

        await MainActor.run {
          self.content = contentDTOs
          // Only show loading if we have no local data
          if contentDTOs.isEmpty {
            self.isLoading = true
          }
        }

        // If we have no local data and user is signed in, fetch from server with loading state
        if contentDTOs.isEmpty && userState.isSignedIn {
          fetchContentFromServer()
        }
      } catch {
        print("Error loading from SwiftData: \(error.localizedDescription)")
        await MainActor.run {
          self.content = []
          if userState.isSignedIn {
            self.isLoading = true
            // Fallback to server fetch if SwiftData fails
            self.fetchContentFromServer()
          }
        }
      }
    }
  }

  // Background sync with server - no loading spinner, just update data silently
  private func backgroundSyncWithServer() {
    // Prevent concurrent syncs
    if isRefreshingFromServer {
      return
    }

    // Don't sync too frequently
    if let lastFetch = lastServerFetchTime,
      Date().timeIntervalSince(lastFetch) < minimumRefreshInterval
    {
      return
    }

    isRefreshingFromServer = true

    Task {
      do {
        // Fetch fresh content from server endpoint
        let fetchedContentDTOs = try await contentService.getLatest(start: 0, size: 50)

        // Remove duplicates
        let uniqueContent = removeDuplicateContent(fetchedContentDTOs)

        // Update the Recent collection in SwiftData
        try await updateRecentCollection(uniqueContent)

        // Update the view model's state silently (no loading spinner)
        // Content is already sorted in CrateActor, just reload from SwiftData
        let updatedCollection = try await crateActor.fetchRecentCollection()
        await MainActor.run {
          self.content = updatedCollection?.contents ?? []
          self.lastServerFetchTime = Date()
          self.isRefreshingFromServer = false
        }
      } catch {
        print("Background sync failed: \(error.localizedDescription)")
        await MainActor.run {
          self.isRefreshingFromServer = false
        }
      }
    }
  }

  // Delete a single content item by id
  public func deleteContent(_ contentItem: ContentDTO) {
    // Skip if content has no server ID
    guard let serverId = contentItem.serverId else {
      // Just remove it locally
      removeContentLocally(contentItem)
      return
    }

    isDeletingContent = true

    // First remove it locally
    removeContentLocally(contentItem)

    // Then delete from server if signed in (using content service)
    if userState.isSignedIn {
      Task {
        do {
          _ = try await contentService.deleteContent(id: serverId)
        } catch {
          print("Error deleting content from server: \(error.localizedDescription)")
        }

        await MainActor.run {
          isDeletingContent = false
        }
      }
    } else {
      isDeletingContent = false
    }
  }

  public func removeContent(at offsets: IndexSet) {
    let contentToDelete = offsets.map { self.content[$0] }

    // Process each content item to delete
    for contentItem in contentToDelete {
      deleteContent(contentItem)
    }
  }

  public func clearAllContent() {
    Task {
      do {
        // Clear from server if signed in
        if userState.isSignedIn {
          _ = try await contentService.deleteAllContent()
        }

        // Clear locally
        try await crateActor.clearRecentCollection()

        await MainActor.run {
          self.content = []
        }
      } catch {
        print("Error clearing all content: \(error.localizedDescription)")
      }
    }
  }

  // Private helper to remove content locally
  private func removeContentLocally(_ contentItem: ContentDTO) {
    Task {
      do {
        // Remove from content array
        content = content.filter { $0.serverId != contentItem.serverId }

        // Remove from Recent collection
        try await crateActor.removeContentFromRecent(contentItem)
      } catch {
        print("Error deleting content locally: \(error.localizedDescription)")
      }
    }
  }

  private func clearAllLocalContent() async {
    do {
      try await crateActor.clearRecentCollection()
      await MainActor.run {
        self.content = []
      }
    } catch {
      print("Error clearing local content: \(error.localizedDescription)")
    }
  }

  // MARK: - Supporting Components

  private func removeDuplicateContent(_ content: [ContentDTO]) -> [ContentDTO] {
    var uniqueContent: [ContentDTO] = []
    var seenContentKeys = Set<String>()

    for item in content {
      // Normalize the comparison to handle slight variations
      guard let title = item.title?.lowercased().trimmingCharacters(in: .whitespacesAndNewlines),
        let detail = item.detail?.lowercased().trimmingCharacters(in: .whitespacesAndNewlines),
        let url = item.url?.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
      else {
        continue  // Skip content missing critical information
      }

      let uniqueKey = "\(title)|\(detail)|\(url)"

      if !seenContentKeys.contains(uniqueKey) {
        uniqueContent.append(item)
        seenContentKeys.insert(uniqueKey)
      }
    }

    return uniqueContent
  }
}
