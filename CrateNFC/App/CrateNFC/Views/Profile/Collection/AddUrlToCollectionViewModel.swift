import CrateServices
import Factory
import Foundation
import SwiftData
import SwiftUI

@MainActor
final class AddUrlToCollectionViewModel: ObservableObject {
  private let contentService: ContentServiceProtocol
  private let collectionService: CollectionServiceProtocol
  private let userState: UserState
  private let crateActor: CrateActor
  private let collection: Collection
  private var debounceWorkItem: DispatchWorkItem?
  private let onContentAdded: (() -> Void)?

  enum State {
    case empty
    case fetching
    case loaded(ContentDTO)
    case error
  }

  struct ViewState {
    var enteredURL: String = ""
    var currentURL: String = ""
    var imageURL: URL?
  }

  struct ActiveFlags: OptionSet {
    let rawValue: Int

    static let showEmptyUrlAlert = ActiveFlags(rawValue: 1 << 0)
  }

  @Published var state: State = .empty
  @Published var viewState = ViewState()
  @Published var activeFlags: ActiveFlags = []

  init(collection: Collection, onContentAdded: (() -> Void)? = nil) {
    self.collection = collection
    self.onContentAdded = onContentAdded
    self.contentService = Container.shared.contentService.resolve()
    self.collectionService = Container.shared.collectionService.resolve()
    self.userState = Container.shared.userState.resolve()
    self.crateActor = Container.shared.crateActor()
  }

  func clearContentInfo() {
    viewState.enteredURL = ""
    viewState.currentURL = ""
    viewState.imageURL = nil
    state = .empty
  }

  func handleURLChange() {
    debounceWorkItem?.cancel()  // Cancel any existing debounce task

    let workItem = DispatchWorkItem { [weak self] in
      guard let self = self else { return }
      guard !self.viewState.currentURL.isEmpty else {
        self.clearContentInfo()
        return
      }

      Task {
        self.state = .fetching

        do {
          let contentData = try await self.contentService.unfurl(url: self.viewState.currentURL)
          self.state = .loaded(contentData)
          if let mediaUrl = contentData.mediaUrl {
            self.viewState.imageURL = URL(string: mediaUrl)
          }
        } catch {
          print("Error unfurling URL: \(error)")
          self.state = .error
        }
      }
    }

    debounceWorkItem = workItem
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: workItem)  // 300ms debounce
  }

  func handleAddToCollection() {
    guard case let .loaded(contentDTO) = state else { return }

    Task {
      do {
        // Use CrateActor to safely add content to collection
        guard let collectionServerId = collection.serverId else {
          print("Collection has no serverId, cannot add content")
          return
        }

        try await crateActor.addContent(contentDTO, toCollectionWithServerId: collectionServerId)

        // Sync with server if user is signed in and content has serverId
        if userState.isSignedIn, let contentServerId = contentDTO.serverId {
          let success = try await collectionService.addContentToCollection(
            collectionServerId: collectionServerId,
            contentServerId: contentServerId
          )

          if !success {
            print("Failed to add content to collection on server")
            // Note: CrateActor has already handled the local update,
            // so we'd need to implement a rollback mechanism if needed
          }
        }

        // Notify parent view that content was added
        onContentAdded?()
      } catch {
        print("Error adding content to collection: \(error)")
      }
    }
  }
}
