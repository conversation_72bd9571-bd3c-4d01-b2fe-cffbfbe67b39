import CrateServices
import Factory
import MSAL
import SwiftUI

@MainActor
public final class ProfileLoginViewModel: ObservableObject {
  @Published public var email: String = ""
  @Published public var password: String = ""
  @Published public var isLoading: Bool = false

  private let HTTPClient: HTTPClient
  private let userService: UserServiceProtocol
  private let userState: UserState

  private let collectionService: CollectionServiceProtocol
  var nativeAuth: MSALNativeAuthPublicClientApplication!

  public init(
    userService: UserServiceProtocol? = nil,
    httpClient: HTTPClient? = nil,
    userState: UserState? = nil,
    collectionService: CollectionServiceProtocol? = nil
  ) {
    self.userService = userService ?? Container.shared.userService.resolve()
    HTTPClient = httpClient ?? Container.shared.httpClient.resolve()
    self.userState = userState ?? Container.shared.userState.resolve()
    self.collectionService = collectionService ?? Container.shared.collectionService.resolve()
  }

  public func handleLogin() async throws -> (User, String) {
    await MainActor.run { isLoading = true }

    do {
      // Use the email and password from the form fields
      let (userDTO, token) = try await userService.login(email: email, password: password)
      let user = userDTO.toModel()
      print("Login successful for user: \(user.email ?? "unknown")")

      await MainActor.run { isLoading = false }  // Set to false on success
      return (user, token)
    } catch {
      await MainActor.run { isLoading = false }  // Set to false on error
      throw error
    }
  }
}
