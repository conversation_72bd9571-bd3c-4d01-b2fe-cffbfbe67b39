import Foundation

extension UserDefaults {
  enum Keys {
    static let serverURL = "serverURL"
    static let showExtraDetails = "showExtraDetails"
  }

  var serverURL: String? {
    get { string(forKey: Keys.serverURL) }
    set { set(newValue, forKey: Keys.serverURL) }
  }

  var showExtraDetails: Bool {
    get { bool(forKey: Keys.showExtraDetails) }
    set { set(newValue, forKey: Keys.showExtraDetails) }
  }
}
