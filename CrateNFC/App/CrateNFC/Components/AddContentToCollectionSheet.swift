import CrateServices
import Factory
import SwiftData
import SwiftUI

struct AddContentToCollectionSheet: View {
  let content: ContentDTO
  let onDismiss: () -> Void
  @StateObject private var viewModel: CollectionViewModel
  @StateObject private var userState = Container.shared.userState.resolve()
  @Environment(\.dismiss) private var dismiss
  @State private var showingCreateCollectionSheet = false
  @State private var showingLoginSheet = false
  @State private var newCollectionName = ""

  init(content: ContentDTO, onDismiss: @escaping () -> Void) {
    self.content = content
    self.onDismiss = onDismiss
    let collectionService = Container.shared.collectionService.resolve()
    _viewModel = StateObject(
      wrappedValue: CollectionViewModel(
        collectionService: collectionService
      ))
  }

  var body: some View {
    NavigationView {
      mainContent
    }
    .onAppear {
      Task {
        await viewModel.loadCollections()
      }
    }
  }

  @ViewBuilder
  private var mainContent: some View {
    VStack(spacing: 0) {
      if viewModel.isLoading {
        loadingView
      } else {
        collectionsList
      }
    }
    .navigationTitle("Add to Collection")
    .navigationBarTitleDisplayMode(.inline)
    .toolbar {
      ToolbarItem(placement: .navigationBarTrailing) {
        Button("Cancel") {
          onDismiss()
          dismiss()
        }
      }
    }
    .sheet(isPresented: $showingCreateCollectionSheet) {
      createCollectionSheet
    }
    .sheet(isPresented: $showingLoginSheet) {
      ProfileLoginView()
    }
  }

  @ViewBuilder
  private var loadingView: some View {
    ProgressView()
      .frame(maxWidth: .infinity, maxHeight: .infinity)
  }

  @ViewBuilder
  private var collectionsList: some View {
    List {
      if userState.isSignedIn {
        createNewCollectionButton

        if viewModel.collections.isEmpty {
          emptyCollectionsView
        } else {
          ForEach(viewModel.collections, id: \.id) { collection in
            collectionRow(collection)
          }
        }
      } else {
        unauthenticatedCollectionView
      }
    }
    .listStyle(InsetGroupedListStyle())
  }

  @ViewBuilder
  private var createNewCollectionButton: some View {
    Button {
      showingCreateCollectionSheet = true
    } label: {
      HStack {
        Image(systemName: "plus.circle.fill")
          .foregroundColor(.blue)
        Text("Create New Collection")
          .foregroundColor(.blue)
      }
    }
    .listRowBackground(Color(UIColor.secondarySystemGroupedBackground))
  }

  @ViewBuilder
  private var emptyCollectionsView: some View {
    Text("No collections available")
      .foregroundColor(.secondary)
      .listRowBackground(Color.clear)
  }

  @ViewBuilder
  private func collectionRow(_ collection: CollectionDTO) -> some View {
    Button {
      Task {
        try? await viewModel.addContentToCollection(collection, contentToAddDTO: content)
        onDismiss()
        dismiss()
      }
    } label: {
      HStack {
        VStack(alignment: .leading, spacing: 4) {
          Text(collection.name ?? "Unnamed Collection")
            .font(.headline)
            .foregroundColor(.primary)

          Text(
            "\(collection.contents?.count ?? 0) item\(collection.contents?.count == 1 ? "" : "s")"
          )
          .font(.subheadline)
          .foregroundColor(.secondary)
        }

        Spacer()

        if let contents = collection.contents,
           contents.contains(where: { $0.serverId == content.serverId }) {
          Image(systemName: "checkmark.circle.fill")
            .foregroundColor(.blue)
        }
      }
      .contentShape(Rectangle())
    }
    .buttonStyle(PlainButtonStyle())
  }

  @ViewBuilder
  private var createCollectionSheet: some View {
    NavigationView {
      VStack(spacing: 20) {
        TextField("Collection Name", text: $newCollectionName)
          .padding()
          .background(Color(UIColor.secondarySystemBackground))
          .cornerRadius(8)
          .padding(.horizontal)
          .disabled(viewModel.isLoading)

        if viewModel.isLoading {
          ProgressView("Creating collection...")
        }

        Spacer()
      }
      .padding(.top, 20)
      .navigationTitle("New Collection")
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarLeading) {
          Button("Cancel") {
            showingCreateCollectionSheet = false
            newCollectionName = ""
          }
          .disabled(viewModel.isLoading)
        }

        ToolbarItem(placement: .navigationBarTrailing) {
          Button("Create") {
            guard !newCollectionName.isEmpty else { return }

            Task {
              do {
                try await viewModel.createCollection(name: newCollectionName)
                if let newCollection = viewModel.collections.last {
                  try await viewModel.addContentToCollection(
                    newCollection, contentToAddDTO: content)
                  newCollectionName = ""
                  showingCreateCollectionSheet = false
                  onDismiss()
                  dismiss()
                }
              } catch {
                print("Error creating collection: \(error)")
              }
            }
          }
          .disabled(newCollectionName.isEmpty || viewModel.isLoading)
        }
      }
    }
  }

  @ViewBuilder
  private var unauthenticatedCollectionView: some View {
    VStack(spacing: 16) {
      Text("Sign in to add content to collections")
        .font(.subheadline)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)

      Button(
        action: {
          showingLoginSheet = true
        },
        label: {
          HStack {
            Image(systemName: "person.crop.circle")
            Text("Sign In")
          }
          .font(.subheadline)
          .foregroundColor(.white)
          .padding(.horizontal, 20)
          .padding(.vertical, 10)
          .background(Color.blue)
          .cornerRadius(8)
        }
      )
    }
    .frame(maxWidth: .infinity)
    .padding()
    .listRowBackground(Color.clear)
  }
}
